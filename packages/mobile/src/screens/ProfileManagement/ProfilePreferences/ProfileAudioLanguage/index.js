import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import useTranslate from 'mobile/src/hooks/useTranslate';
import useAuth from 'mobile/src/hooks/useAuth';
import ProfilePreferenceList from '../../Components/ProfilePreferenceList';
import ProfilePreferenceLanguageItem from '../../Components/ProfilePreferenceLanguageItem';
import { updateProfileAudioPreference } from '@video-ready/common/services/auth/action';

const ProfileAudioLanguage = () => {
  const { getText } = useTranslate();
  const dispatch = useDispatch();
  const { profileId, accessToken } = useAuth();

  // Subscribe to only audioLanguages from Redux
  const audioLanguagesFromAPI = useSelector(
    (state) => state.auth.profilePreferences?.audioLanguages || []
  );
  const [selectedLanguage, setSelectedLanguage] = useState('English');
  const [audioLanguages, setAudioLanguages] = useState([]);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    // Transform API data to component format
    if (audioLanguagesFromAPI?.length) {
      const transformedLanguages = audioLanguagesFromAPI.map((lang) => ({
        id: lang.languageId,
        name: lang.name,
        localName: lang.localName,
        codes: lang.codes,
        selected: lang.languageId === 'en', // Default to English
      }));
      setAudioLanguages(transformedLanguages);
    }
  }, [audioLanguagesFromAPI]);

  const onLanguageSelect = (language) => {
    setSelectedLanguage(language.name);
    // Save audio preference to API
    if (profileId && accessToken) {
      setSaving(true);
      const audioPreferences = {
        languageId: language.id,
        // You can add bitrateId here if needed
        // bitrateId: "300"
      };

      dispatch(updateProfileAudioPreference(
        profileId,
        audioPreferences,
        accessToken,
        (response) => {
          console.log('Audio preference saved successfully:', response);
          setSaving(false);
        },
        (error) => {
          console.error('Failed to save audio preference:', error);
          setSaving(false);
        }
      ));
    }

    console.log('Selected audio language:', language);
  };

  const renderItemContent = (item) => (
    <ProfilePreferenceLanguageItem item={item} />
  );

  return (
    <ProfilePreferenceList
      title={getText('audio_language')}
      data={audioLanguages}
      selectedValue={selectedLanguage}
      onItemSelect={onLanguageSelect}
      renderItemContent={renderItemContent}
      disabled={saving}
    />
  );
};

export default ProfileAudioLanguage;
