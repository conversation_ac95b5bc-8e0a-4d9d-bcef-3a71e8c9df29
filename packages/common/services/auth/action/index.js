import {
  sendGetRequest,
  postUserLogout,
  sendPostRequest,
  extendTrailPostRequest,
  getRequestAuthenticated,
  postRequestAuthenticated,
  sendPostRequestWithDeviceDetails,
  deleteRequestAuthenticated,
  sendPostRequestWithHeaders,
  sendPatchRequest,
  patchRequestAuthenticated,
  putRequestAuthenticated,
} from "@video-ready/common/apiManager";
import { authConstants, GRANT_TYPES } from "../constants";
import appConfig from "../../../utils/config";
import urlManager from "../../../utils/configURL";
import { toggleAppLoader } from "../../miscellaneous/action";
import { STORE_FAVOURTIES_DATA } from "../../home/<USER>";
import ERROR_CODES from "@video-ready/common/utils/errorCode";
import { apiConstants } from "@video-ready/common/apiManager/apiConfig";
import {
  getTimeZone,
  getPlatformID,
  filterPlatformSpecificData,
  generateRandomValue,
  getCurrentTimestamp,
} from "@video-ready/common/utils/commonMethods";
import { checkTVPack, filterUserEntitlement } from "@video-ready/common/utils/storeFrontHelpers";
import CONSTANTS from "@video-ready/common/utils/constant";
import ENV from "@video-ready/common/config/env/env.json";
import { getStoreFrontProducts } from "@video-ready/common/services/home/<USER>";
import LocalStorageService from "mobile/src/utils/LocalStorageService";
import { ACTIVE_TOKEN_TYPES, AsyncStorageKey } from "mobile/src/utils";
import { amInstance, instance } from "../../../apiManager/axios";

const pageSize = 10;

export const fetchMenuDataListByPlatform =
  (platformOS, isTv = false, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let platform = platformOS.id ?? getPlatformID(platformOS, isTv);
    let url = `${apiConstants.LIST_BY_PLATFORM}${platform}`;
    try {
      const res = await sendGetRequest(url);
      if (res?.status) {
        dispatch({
          type: authConstants.GET_MENU_DATA_LIST,
          payload: res?.data?.menuDataList,
        });
        callback?.(res?.data?.menuDataList);
      } else {
        err(res);
        console.error(res, "fetchMenuDataListByPlatform Api error");
      }
      return res;
    } catch (error) {
      console.error(error, "fetchMenuDataListByPlatform Api Failed");
      err(error);
    }
  };

export const fetchFooterByPlatform =
  (platformOS, isTv = false, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let platform = platformOS.id ?? getPlatformID(platformOS, isTv);
    let url = `${apiConstants.HOMESCREEN_FOOTER}${platform}`;
    try {
      const res = await sendGetRequest(url);
      if (res?.status) {
        dispatch({
          type: authConstants.HOME_SCREEN_FOOTER,
          payload: res?.data,
        });
        callback?.(res?.data);
      } else {
        err(res);
        console.error(res, "fetchFooterByPlatform Api Failed");
      }
      return res;
    } catch (error) {
      console.error(error, "fetchFooterByPlatform Api error");
      err(error);
    }
  };

export const fetchPlatformSpecificMenuData =
  (platformId, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let url = `${apiConstants.LIST_BY_PLATFORM}${platformId}`;
    try {
      const res = await sendGetRequest(url);
      dispatch(toggleAppLoader(false));
      if (res?.status) {
        dispatch({
          type: authConstants.GET_MENU_DATA_LIST,
          payload: res?.data?.menuDataList,
        });
        callback?.(res?.data?.menuDataList);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      dispatch(toggleAppLoader(false));
      err(error);
    }
  };

export const getSmartTvCode =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let url = `${apiConstants.SMART_TV_CODE}`;
    try {
      const res = await sendGetRequest(url, params);

      dispatch(toggleAppLoader(false));
      if (res?.status) {
        callback?.(res);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const getSmartTvStatus =
  (body, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let url = apiConstants.SMART_TV_STATUS;
    try {
      const res = await sendGetRequest(url, body);
      dispatch(toggleAppLoader(false));
      if (res?.status) {
        const payload = {
          data: {
            subscriber: res?.data.userDetails ?? {},
            tokenDetails: {
              accessToken: res?.data?.accessToken,
              refreshToken: res?.data?.refreshToken,
              defaultProfileId: res?.data.defaultProfileId ?? "",
            },
          },
        };
        if (res?.status) {
          dispatch({
            type: authConstants.SET_SIGN_IN_DATA,
            payload: payload,
          });
        }
        callback?.(res);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const validateTvCode =
  (params, accessToken, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let url = apiConstants.VALIDATE_SMART_TV_CODE;
    try {
      const res = await postRequestAuthenticated(url, params, accessToken);
      if (res?.status) {
        callback?.(res);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const fetchConfigId =
  (platform, success = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendGetRequest(apiConstants.CONFIG_ID);

      if (!res?.status) {
        dispatch({
          type: authConstants.GET_MENU_DATA_LIST,
          payload: [],
        });
        return;
      }

      const data = res?.data?.[0]?.data;

      const appUpgradeData = data?.appUpgrade;
      const platformSpecificId = data?.platformId;
      const qrCodeImages = data?.config?.qrCodeLogin;
      const railCartSize = data?.config?.railCartSize;
      const tenantInfo = data?.config?.tenant_basic_config;
      const continueWatching = data?.config?.continueWatching;
      const inactivePlayback = data?.config?.inactivePrompt;
      const imageService = data?.config?.imageService;
      const drmCertificateInfo = data?.config?.drmCertificateInfo;
      const csaiInfo = {
        csaiVOD: data?.csaiVOD,
        csaiLinear: data?.csaiLinear,
      };
      const operatorColorCodes = data?.operatorConfig;
      const recommendationId = data?.config?.recommendation;
      const subscriptionUrlInfo = data?.config?.subscription;
      const isDrmLicenseRequired = data?.isDrmLicenseRequired;
      const priceBasedUpgradeDowngrade = data?.priceBasedUpgradeDowngrade;
      const appList = data?.appLanguage ?? {};
      const audioList = data?.audioLanguage ?? {};
      const subtitleList = data?.subtitleLanguage ?? {};
      const isRootCheck = data?.isRootedCheck;
      const imageHeightR = data?.imageHeightR;
      const imageWidthS = data?.imageWidthS;
      const operatorConfig = data?.operatorConfig;
      const resolutionConfig = data?.resolutionConfig;
      const initialBitrate = data?.config?.initial_abr;
      appConfig.setAppConfig({
        ...(data && data),
        csaiInfo: csaiInfo,
        operatorConfig: operatorConfig,
        platform: res?.data?.[0]?.platform,
        languageList: audioList?.languages,
        appLanguageList: appList?.languages,
        audioLanguageList: audioList?.languages,
        subtitleLanguageList: subtitleList?.languages,
      });

      urlManager.setURL(imageService);
      // dispatch platform specific id
      dispatch({
        type: authConstants.SET_VIDEO_INITIAL_BITRATE,
        payload: initialBitrate,
      });
      dispatch({
        type: authConstants.PLATFORM_ID,
        payload: platformSpecificId,
      });
      dispatch({
        type: authConstants.SET_APP_UPGRADE_DATA,
        payload: appUpgradeData,
      });
      dispatch({
        type: authConstants.GET_QR_CODE_IMAGES,
        payload: qrCodeImages,
      });
      dispatch({
        type: authConstants.RECOMMENDATION_ID,
        payload: recommendationId,
      });

      dispatch({
        type: authConstants.SUBSCRIPTION_INFO,
        payload: subscriptionUrlInfo,
      });
      dispatch({
        type: authConstants.GET_RAIL_CART_SIZE,
        payload: railCartSize,
      });
      dispatch({
        type: authConstants.TENANT_USER_INFO,
        payload: tenantInfo?.sub_title_visible,
      });
      dispatch({
        type: authConstants.SET_CONFIG_INFO,
        payload: [data],
      });
      dispatch({
        type: authConstants.SET_CONTINUE_WATCHING_CONFIG_DATA,
        payload: continueWatching,
      });
      dispatch({
        type: authConstants.INACTIVE_PLAYBACK,
        payload: inactivePlayback,
      });
      dispatch({
        type: authConstants.DRM_CERTIFICATE_INFO,
        payload: drmCertificateInfo,
      });
      dispatch({
        type: authConstants.CSAI_INFO,
        payload: csaiInfo,
      });
      dispatch({
        type: authConstants.OPERATOR_COLOR_CODES,
        payload: operatorColorCodes,
      });
      dispatch({
        type: authConstants.ROOT_CHECK,
        payload: isRootCheck,
      });
      dispatch({
        type: authConstants.IMAGEHEIGHTR,
        payload: imageHeightR,
      });
      dispatch({
        type: authConstants.IMAGEWIDTHS,
        payload: imageWidthS,
      });
      dispatch({
        type: authConstants.AUDIO_SUBTITLE_LIST,
        payload: {
          [CONSTANTS?.LANGUAGE_TYPES?.APP_LANGUAGE]: appList,
          [CONSTANTS?.LANGUAGE_TYPES?.AUDIO_LANGUAGE]: audioList,
          [CONSTANTS?.LANGUAGE_TYPES?.SUBTITLE_LANGUAGE]: subtitleList,
        },
      });
      dispatch({
        type: authConstants.PRICE_BASED_UPGRADE_DOWNGRADE,
        payload: priceBasedUpgradeDowngrade,
      });
      dispatch({
        type: authConstants.RESOLUTION_CONFIG,
        payload: resolutionConfig,
      });
      success(platformSpecificId, appUpgradeData, qrCodeImages, data);
      return res;
    } catch (error) {
      console.error(error);
      err(error);
    }
  };

export const fetchHtml =
  (params, callback = () => {}, err = () => {}) =>
  async () => {
    try {
      const res = await sendGetRequest(apiConstants.GET_HTML(params));
      if (res.status) {
        callback(res?.data);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const fetchFaq =
  (params, callback = () => {}, err = () => {}) =>
  async () => {
    try {
      const res = await sendGetRequest(apiConstants.GET_FAQ(params));
      if (res.status) {
        callback(res?.data);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const fetchSignInWithOTP =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.SIGN_IN_WTIH_OTP, params);
      if (res?.status) {
        callback?.(res?.data);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };
export const fethSignInWithOTP =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.SIGN_IN_WTIH_OTP, params);
      if (res?.status) {
        callback?.(res?.data);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };
export const fetchSignUp =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.SIGN_UP_WITH_OTP, params);
      if (res?.status) {
        callback(res?.data);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const resendOtp =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.RESEND_OTP, params);
      if (res?.status) {
        callback(res.data);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const getLanguageList =
  (callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      let url = `${apiConstants.LANGUAGE_LIST}`;
      const res = await sendGetRequest(url);
      if (res.status) {
        callback(res.data);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const searchAccount = async (params, deviceDetails, callback = () => {}, err = () => {}) => {
  try {
    const res = await sendPostRequestWithDeviceDetails(
      apiConstants.SEARCH_ACCOUNT,
      params,
      deviceDetails
    );
    callback(res);
  } catch (error) {
    err(error);
  }
};

export const forgotPassword = async (
  params,
  deviceDetails,
  callback = () => {},
  err = () => {}
) => {
  try {
    const res = await sendPostRequestWithDeviceDetails(
      apiConstants.FORGOT_PASSWORD,
      params,
      deviceDetails
    );
    callback(res);
  } catch (error) {
    err(error);
  }
};

export const resetPassword = async (
  params,
  deviceDetails = {},
  callback = () => {},
  err = () => {}
) => {
  try {
    const res = await sendPostRequestWithDeviceDetails(
      apiConstants.RESET_PASSWORD,
      params,
      deviceDetails
    );
    callback(res);
  } catch (error) {
    err(error);
  }
};

export const changePassword = async (params, accessToken, callback = () => {}, err = () => {}) => {
  try {
    const res = await postRequestAuthenticated(apiConstants.CHANGE_PASSWORD, params, accessToken);
    callback(res);
  } catch (error) {
    err(error);
  }
};

export const updatePassword = async (params, accessToken, callback = () => {}, err = () => {}) => {
  try {
    const res = await postRequestAuthenticated(apiConstants.UPDATE_PASSWORD, params, accessToken);
    callback(res);
  } catch (error) {
    err(error);
  }
};

export const fetchLogOut = (accessToken, deviceDetails) => {
  return postUserLogout(apiConstants.LOGOUT_USER, accessToken, deviceDetails)
    .then((res) => {
      if (res?.status) {
        return res;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

/**
 *
 * @param {phoneNumber/email, locale} params
 * @param {data} callback
 * @returns
 */
export const fetchPasswordOtp =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.FORGOT_PASSWORD_OTP, params);
      if (res?.status) {
        callback(res?.data, res?.message);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

/**
 * @param {email/phone, name, password}
 * @param {success message} callback
 * @param {faild message} err
 * @returns
 */
export const saveSignUpData =
  (params, headers, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.SAVE_SIGN_UP_DATA, params, headers);
      if (res?.status) {
        callback(res?.data);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const otpVerify =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.VALIDATE_OTP, params);
      if (res?.status) {
        // localStorage.setItem("jwtToken",res?.data?.data?.jwtResponseToken)
        callback(res?.data, res?.message);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const getOAuthAccessToken =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.GET_AUTHOACCESSTOKEN, params);
      if (res?.status) {
        // localStorage.setItem("jwtToken",res?.data?.data?.jwtResponseToken)
        callback(res?.data);
        dispatch({
          type: authConstants.SET_SIGN_IN_DATA,
          payload: {
            data: res?.data,
          },
        });
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const searchPageDataTv =
  (params, search, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      let url = `${apiConstants.RECENT_SEARCH}${search}`;
      const res = await sendGetRequest(url, params);
      if (res.status) {
        callback(res.data);

        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const searchPageData =
  (search, callback = () => {}, err = () => {}) =>
  async (dispatch, getState) => {
    try {
      let url = `${apiConstants.RECENT_SEARCH}${search}`;
      const res = await sendGetRequest(url);
      const contentRightInfo = getState()?.home?.contentRightInfo;
      if (res.status) {
        try {
          res.data = res.data.map((item) => {
            if (item.assetId) {
              item["isPremium"] = false;
              if (contentRightInfo && contentRightInfo[item.assetId]) {
                item["isPremium"] = true;
                item["packageName"] = contentRightInfo[item.assetId][0].packageName;
              }
            }
            return item;
          });
          dispatch({
            type: authConstants.GET_SEARCH_PAGE_DATA,
            payload: res.data,
          });
          callback(res.data);
        } catch (error) {
          //
          console.error(error);
        }

        return;
      } else {
        err(res);
      }
    } catch (error) {
      console.error(error);
      err(error);
    }
  };

export const searchByFilter =
  (data, callback = () => {}, err = () => {}) =>
  async (dispatch, getState) => {
    try {
      let url = `${apiConstants.SEARCH_BY_FILTER}`;
      const res = await sendPostRequest(url, data);
      if (res) {
        dispatch({ type: authConstants.GET_SEARCH_PAGE_DATA, payload: res });
        callback && callback(res);
        return res;
      } else {
        err(res);
        return res;
      }
    } catch (error) {
      console.error(error);
      err(error);
      return error;
    }
  };

export const globalSearch = async (data, userEntitlements = [], platformName = "Mobile") => {
  let url = `${apiConstants.SEARCH_BY_FILTER}`;
  return new Promise((resolve, reject) => {
    sendPostRequest(url, data)
      .then((res) => {
        if (res?.contents?.length > 0) {
          let filteredData = filterPlatformSpecificData(
            res?.contents,
            userEntitlements,
            platformName
          );
          // res.contents = filteredData;
        }
        resolve(res);
      })
      .catch((error) => {
        console.log("error search", error);
        reject(error);
      });
  });
};

export const searchFilters =
  (search, callback = () => {}, err = () => {}) =>
  async (dispatch, getState) => {
    try {
      let url = `${apiConstants.SEARCH_FILTER}`;
      const res = await sendGetRequest(url);
      if (res.status) {
        dispatch({ type: authConstants.GET_SEARCH_FILTER, payload: res.data });
        return res.data;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const setLoader = () => {
  return {
    type: apiConstants.SET_LOADER,
  };
};

export const removeLoader = () => {
  return {
    type: apiConstants.REMOVE_LOADER,
  };
};

export const setApiErrorModal = (data) => {
  return {
    type: authConstants.Api_Error_Modal,
    payload: data,
  };
};

export const reelData =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let data = {
      pageSIze: pageSize,
      contentType: "REELS",
      ...params,
    };
    try {
      const res = await sendPostRequest(apiConstants.PLAY_REELS, data);
      if (res?.status) {
        callback(res?.data);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const getUserEntitlements =
  (params, accessToken, callback = () => {}, err = () => {}) =>
  async (dispatch, getState) => {
    try {
      const res = await postRequestAuthenticated(
        apiConstants.GET_USER_ENTITLEMENTS,
        params,
        accessToken,
        false
      );
      if (res?.status) {
        dispatch({
          type: authConstants.SET_USER_ENTITLEMENTS,
          payload: res?.data,
        });
        callback(res?.data);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const getContentPlayableInfo =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const body = {
        headers: {
          ["X-TIMEZONE"]: getTimeZone(),
        },
      };
      const res = await sendPostRequest(apiConstants.IS_CONTENT_PLAYABLE_INFO, params, body);
      if (res?.status) {
        callback(res?.data);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };
export const callRefreshToken = (refreshToken, subscriberInfo, callback) => async (dispatch) => {
  try {
    const body = { refreshToken };
    const response = await postRequestAuthenticated(apiConstants.refreshToken, body);
    if (response?.data) {
      const updatedSignedInResponse = {
        subscriber: subscriberInfo,
        tokenDetails: { ...response?.data },
      };
      dispatch({
        type: authConstants.SET_SIGN_IN_DATA,
        payload: updatedSignedInResponse,
      });
      callback(true, response?.data);
    } else {
      callback(false, null);
    }
  } catch (error) {
    callback(false, null);
    console.error("Failed to refresh token:", error);
  }
};

export const fetchSignOut =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      let url = `${apiConstants.SIGN_OUT}`;

      const res = await getRequestAuthenticated(url, params.macAddress, params.accessToken);
      if (res?.status) {
        callback();
        return;
      } else {
        err(res);
      }
    } catch (error) {}
  };

export const getLiveChannels =
  (id, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      let url = `${apiConstants.LIVE_CHANNELS(id)}`;
      const res = await sendGetRequest(url);
      if (res.status) {
        callback(res?.data?.results);
      }
    } catch (error) {
      err(error);
    }
  };

export const checkDeepLink =
  (val = false) =>
  (dispatch) => {
    dispatch({
      type: authConstants.IS_DEEP_LINK_TRIGGERED,
      payload: val,
    });
  };

export const fetchTenantInfo =
  (callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let pathName = process.env.tenantUrl;
    let url = `${apiConstants.TENANT_URL(pathName)}`;
    try {
      const res = await sendGetRequest(url);
      if (res?.status) {
        callback?.(res?.data);
      } else {
        err(res);
      }
      dispatch({
        type: authConstants.TENANT_INFO,
        payload: res?.data ?? res,
      });
      return res?.data ?? res;
    } catch (error) {
      console.error("Fetch tenant API info ", error);
      err(error);
      return error;
    }
  };

export const extendTrail =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await extendTrailPostRequest(apiConstants.EXTEND_TRAIL, params);
      callback(res);
      return;
    } catch (error) {
      err(error);
    }
  };

export const storeFavouritesData = (id, isFavourite) => ({
  type: STORE_FAVOURTIES_DATA,
  payload: { id, isFavourite },
});

export const searchAutoComplete =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let url = `${apiConstants.AUTO_COMPLETE}?searchTerm=${params.searchTerm}`;
    try {
      const res = await sendGetRequest(url);
      if (res) {
        callback && callback(res);
        return res;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const searchAutoCompleteUpdated =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let data = {
      indexName: "content_auto_complete",
    };
    try {
      const encodParam = encodeURIComponent(params?.searchTerm);
      const res = await sendGetRequest(
        `${apiConstants.AUTO_COMPLETE}?limit=${params?.limit}&offset=${params?.offset}&searchTerm=${encodParam}`
      );
      if (res) {
        callback && callback(res);
        return res;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

// calling evergent mock APIs
export const fetchSignInWithPasswordUpdated =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.SIGN_WITH_PASSWORD_UPDATED, params);
      if (res?.status) {
        callback(res?.data);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const fetchSignInWithPassword =
  (params, deviceDetails = {}, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequestWithDeviceDetails(
        apiConstants.SIGN_WITH_PASSWORD_UPDATED,
        params,
        deviceDetails
      );
      if (res?.status) {
        let userInfo = {};
        if (res?.errorCode !== ERROR_CODES.ERR_100_125) {
          const accesssToken = res?.data?.accessToken;
          const customerId = res?.data?.userDetails?.customerId;
          userInfo = await dispatch(getUserDetails(accesssToken, customerId));
        }
        const userData = {
          subscriber: {
            ...res?.data?.userDetails,
            ...userInfo,
            profileId: res?.data?.defaultProfileId,
          },
          tokenDetails: {
            accessToken: res?.data?.accessToken,
            refreshToken: res.data?.refreshToken,
          },
        };
        if (res?.errorCode !== ERROR_CODES.ERR_100_125) {
          dispatch({
            type: authConstants.SET_SIGN_IN_DATA,
            payload: {
              data: userData,
            },
          });
        }
        callback(userData, res);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const updateTokenStorage = ({
  accessToken,
  refreshToken,
  profileId = null,
  tokenType = "Bearer",
  expiresIn,
  entitlementHash = null,
  isProfile = false,
}) => {
  const expiryTime = Date.now() + expiresIn * 1000;

  const tokenPayload = {
    accessToken,
    refreshToken,
    expiryTime,
    tokenType,
  };

  if (isProfile) {
    tokenPayload.profileId = profileId;
    tokenPayload.entitlementHash = entitlementHash;
    LocalStorageService.setItem(AsyncStorageKey.PROFILE, JSON.stringify(tokenPayload));
    LocalStorageService.setItem(AsyncStorageKey.ACTIVE_TOKEN, ACTIVE_TOKEN_TYPES.PROFILE);
  } else {
    LocalStorageService.setItem(AsyncStorageKey.LOGIN, JSON.stringify(tokenPayload));
    LocalStorageService.setItem(AsyncStorageKey.ACTIVE_TOKEN, ACTIVE_TOKEN_TYPES.LOGIN);
  }

  // Always update Axios with the profile token if available
  instance.defaults.headers.common["Authorization"] = `Bearer ${accessToken}`;
  amInstance.defaults.headers.common["Authorization"] = `Bearer ${accessToken}`;
};

export const fetchProfiles =
  (accessToken, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendGetRequest(apiConstants.AM_PROFILES);
      if (res) {
        callback(res);
        return res;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const getProfileToken =
  (accessToken, profileId, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Bearer ${accessToken}`,
      };

      const params = new URLSearchParams({
        grant_type: GRANT_TYPES.TOKEN_EXCHANGE,
        subject_token: accessToken,
        subject_token_type: "Bearer",
        profile_id: profileId,
      });
      const res = await sendPostRequestWithHeaders({
        url: apiConstants.AM_TOKEN,
        body: params,
        headers,
      });

      if (!res?.access_token) {
        err({ message: "Failed to exchange profile token." });
        return;
      }

      updateTokenStorage({
        accessToken: res.access_token,
        refreshToken: res.refresh_token,
        profileId,
        tokenType: res.token_type,
        expiresIn: res.expires_in,
        entitlementHash: res.entitlementHash,
        isProfile: true,
      });

      callback(res);
      return res;
    } catch (error) {
      err(error);
    }
  };

export const fetchEntitlements =
  (accessToken, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendGetRequest(apiConstants.AM_ENTITLEMENTS);
      if (res) {
        callback(res);
        return res;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const fetchSSOToken =
  (params, deviceDetails = {}, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const header = { "Content-Type": "application/x-www-form-urlencoded" };
      // Convert params object to URLSearchParams string
      const formData = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        formData.append(key, value);
      });

      const res = await sendPostRequestWithHeaders({
        url: apiConstants.AM_TOKEN,
        body: formData.toString(),
        headers: header,
      });

      if (!res?.access_token) {
        err({ message: "Failed to retrieve login access token." });
        return;
      }

      updateTokenStorage({
        accessToken: res.access_token,
        refreshToken: res.refresh_token,
        tokenType: res.token_type,
        expiresIn: res.expires_in,
        isProfile: false,
      });

      const profilesData = await dispatch(fetchProfiles(res.access_token));

      const selectedProfileId = profilesData?.profiles?.[0]?.profileId;

      if (!selectedProfileId) {
        err({ message: "No profile ID found in profile response." });
        return;
      }

      const profileRes = await dispatch(getProfileToken(res.access_token, selectedProfileId));

      if (!profileRes?.access_token) {
        err({ message: "Profile token exchange failed." });
        return;
      }

      // Optionally fetch entitlements
      const entitlementsData = await dispatch(fetchEntitlements(profileRes.access_token));
      const { account = {}, profile = {} } = entitlementsData;
      const { accountId = "" } = account;
      const { profileId = "" } = profile;

      const subscriberData = {
        customerId: accountId,
        profileId: profileId,
      };

      LocalStorageService.setItem(AsyncStorageKey.SUBSCRIBER_INFO, subscriberData);
      LocalStorageService.setItem(AsyncStorageKey.SUBSCRIBER_FULL_INFO, subscriberData);

      dispatch({
        type: authConstants.SET_SIGN_IN_DATA,
        payload: {
          data: {
            subscriber: {},
            tokenDetails: {
              accessToken: profileRes.access_token,
              refreshToken: profileRes.refresh_token,
              defaultProfileId: profileId,
              expiresIn: profileRes.expires_in,
              tokenType: profileRes.token_type,
              entitlementHash: profileRes.entitlementHash,
            },
            profiles: profilesData,
            entitlements: entitlementsData,
          },
        },
      });

      LocalStorageService.setItem("profileId", profileId);

      callback(res);
    } catch (error) {
      err(error);
    }
  };

export const redirectToSSOLogin = async () => {
  const nonce = generateRandomValue(10);
  const state = `PAYTV-${CONSTANTS.PLATFORM_TYPE.WEB.toUpperCase()}`;
  const url = `${ENV.baseUrl + apiConstants.LOGIN_AUTHORIZE}?nonce=${nonce}&state=${state}`;
  window.location.assign(url);
};

export const socialLogin =
  (params, deviceDetails = {}, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequestWithDeviceDetails(
        apiConstants.SOCIAL_LOGIN,
        params,
        deviceDetails
      );
      if (res?.status) {
        const accesssToken = res?.data?.accessToken;
        const customerId = res?.data?.userDetails?.customerId;
        let userInfo = "";
        if (
          res?.successCode !== ERROR_CODES.SUC_100_174 &&
          res?.errorCode !== ERROR_CODES.ERR_100_125
        ) {
          userInfo = await dispatch(getUserDetails(accesssToken, customerId));
        }
        const userData = {
          subscriber: {
            ...res?.data?.userDetails,
            ...userInfo,
            profileId: res?.data?.defaultProfileId,
          },
          tokenDetails: {
            accessToken: res?.data?.accessToken,
            refreshToken: res.data?.refreshToken,
          },
        };
        if (
          res?.successCode !== ERROR_CODES.SUC_100_174 &&
          res?.errorCode !== ERROR_CODES.ERR_100_125
        ) {
          dispatch({
            type: authConstants.SET_SIGN_IN_DATA,
            payload: {
              data: userData,
            },
          });
        }
        callback(userData, res);
        return;
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

// calling evergent mock APIs
export const fetchUserData =
  (tokenDetails, callBack = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await postRequestAuthenticated(
        apiConstants.FETCH_USER_DATA,
        null,
        tokenDetails?.accessToken
      );
      if (res?.status) {
        const userData = {
          subscriber: { ...res?.data, email: res?.data?.contactMessage?.[0]?.email },
          tokenDetails,
        };
        dispatch({
          type: authConstants.SET_SIGN_IN_DATA,
          payload: {
            data: userData,
          },
        });
        callBack(userData);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

// calling evergent mock APIs
export const fetchSignInWithOTPUpdated =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.SIGN_WITH_OTP_UPDATED, params);
      if (res?.status) {
        callback?.(res);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

// calling evergent mock APIs
export const otpVerifyUpdated =
  (params, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequest(apiConstants.VALIDATE_OTP_UPDATED, params);
      if (res?.status) {
        // localStorage.setItem("jwtToken",res?.data?.data?.jwtResponseToken)
        callback(res, res?.message);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

export const saveSignUpDataUpdated =
  (params, deviceDetails = {}, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendPostRequestWithDeviceDetails(
        apiConstants.SAVE_SIGN_UP_DATA_UPDATED,
        params,
        deviceDetails
      );
      if (res?.status) {
        const accesssToken = res?.data?.accessToken;
        const customerId = res?.data?.userDetails?.customerId;
        const userInfo = await dispatch(getUserDetails(accesssToken, customerId));
        const userData = {
          subscriber: {
            ...res?.data?.userDetails,
            ...userInfo,
            profileId: res?.data?.defaultProfileId,
          },
          tokenDetails: {
            accessToken: res?.data?.accessToken,
            refreshToken: res.data?.refreshToken,
          },
        };
        // to get the data at profile screen (Mobile) after successful signup
        dispatch({
          type: authConstants.SET_SIGN_IN_DATA,
          payload: {
            data: userData,
          },
        });
        callback(userData);
      } else {
        err(res);
      }
    } catch (error) {
      err(error);
    }
  };

// calling evergent mock APIs
export const getSubscriberDevices = (customerId, deviceDetails, accessToken) => {
  return getRequestAuthenticated(
    apiConstants.LIST_SUBSCRIBER_DEVICES(customerId),
    null,
    accessToken,
    deviceDetails,
    null,
    customerId
  )
    .then((res) => {
      if (res) {
        return res?.data;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const deleteDevice = (customerId, deviceId, accessToken) => {
  /*
  response status codes :
    0 - "Success";
    "DMERR101" - "Data not found";
    "DMERR100" - "Technical failure";
   */

  return deleteRequestAuthenticated(
    apiConstants.DELETE_DEVICE(customerId, deviceId),
    null,
    accessToken
  )
    .then((res) => {
      if (res?.code === 0) {
        return res?.data;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const deleteAllDevices = (customerId, device_id, accessToken) => {
  const userData = [{ customerId, device_id }];

  return postRequestAuthenticated(
    apiConstants.DELETE_ALL_DEVICES(customerId),
    userData,
    accessToken
  )
    .then((res) => {
      if (res?.code === 0) {
        return res?.data;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const setTTLToken = (token) => async (dispatch) => {
  dispatch({
    type: authConstants.TTL_TOKEN,
    payload: token,
  });
};

export const removeDeviceOnPreLogin = (params, accessToken, customerId) => async (dispatch) => {
  return new Promise((resolve, reject) => {
    postRequestAuthenticated(
      apiConstants.REMOVE_DEVICE_PRE_LOGIN,
      params,
      accessToken,
      null,
      null,
      customerId
    )
      .then(async (response) => {
        if (response?.status) {
          const accesssToken = response?.data?.accessToken;
          const customerId = response?.data?.userDetails?.customerId;
          const userInfo = await dispatch(getUserDetails(accesssToken, customerId));
          const userData = {
            subscriber: {
              ...response?.data?.userDetails,
              ...userInfo,
              profileId: response?.data?.defaultProfileId,
            },
            tokenDetails: {
              accessToken: response?.data?.accessToken,
              refreshToken: response.data?.refreshToken,
            },
          };
          dispatch({
            type: authConstants.SET_SIGN_IN_DATA,
            payload: {
              data: userData,
            },
          });
          resolve(userData);
        } else {
          reject(response);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};

export const removeAllDeviceOnPreLogin =
  (accessToken = "", customerId) =>
  async (dispatch) => {
    return new Promise((resolve, reject) => {
      postRequestAuthenticated(
        apiConstants.REMOVE_ALL_DEVICE_PRE_LOGIN,
        null,
        accessToken,
        null,
        null,
        customerId
      )
        .then(async (response) => {
          if (response?.status) {
            const accesssToken = response?.data?.accessToken;
            const customerId = response?.data?.userDetails?.customerId;
            const userInfo = await dispatch(getUserDetails(accesssToken, customerId));
            const userData = {
              subscriber: {
                ...response?.data?.userDetails,
                ...userInfo,
                profileId: response?.data?.defaultProfileId,
              },
              tokenDetails: {
                accessToken: response?.data?.accessToken,
                refreshToken: response.data?.refreshToken,
              },
            };
            dispatch({
              type: authConstants.SET_SIGN_IN_DATA,
              payload: {
                data: userData,
              },
            });
            resolve(userData);
          } else {
            reject(response);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

export const updateProfile = (data, accessToken, cp_id) => async (dispatch) => {
  return new Promise((resolve, reject) => {
    postRequestAuthenticated(apiConstants.UPDATE_PROFILE, data, accessToken, "", null, cp_id)
      .then((response) => {
        if (response?.status) {
          dispatch(getUserDetails(accessToken, cp_id));
          resolve(response);
        } else {
          reject(response);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};

export const checkCredentials = (data, accessToken) => async () => {
  return new Promise((resolve, reject) => {
    postRequestAuthenticated(apiConstants.CHECK_CREDENTIALS, data, accessToken)
      .then((response) => {
        if (response?.status) {
          resolve(response);
        } else {
          reject(response);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};

export const getPaymentMethods = async () => {
  return getRequestAuthenticated(apiConstants.GET_PAYMENT_METHODS)
    .then((res) => {
      if (res?.status) {
        return res?.data;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const verifyCouponCode = async (params) => {
  return postRequestAuthenticated(apiConstants.VALIDATE_COUPON_CODE, params)
    .then((res) => {
      if (res?.status) {
        return res;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const initiatePayment = async (params) => {
  return postRequestAuthenticated(apiConstants.AUTHORIZE_PAYMENT, params)
    .then((res) => {
      if (res.status) {
        return res?.data;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const getProductsList =
  (params, storeInRedux = false, onCallBack = () => {}, onError = () => {}) =>
  async (dispatch) => {
    return sendPostRequest(apiConstants.PRODUCTS, params)
      .then((res) => {
        if (res.status) {
          onCallBack(res.data);
          storeInRedux && dispatch({ type: authConstants.PRODUCT_LIST, payload: res.data });
          return res?.data;
        } else {
          onError();
          throw res;
        }
      })
      .catch((error) => {
        onError(error);
        throw error;
      });
  };

export const getUserProductsList =
  (params, storeInRedux = false) =>
  async (dispatch) => {
    return postRequestAuthenticated(apiConstants.USER_PRODUCTS, params)
      .then((res) => {
        if (res.status) {
          storeInRedux && dispatch({ type: authConstants.PRODUCT_LIST, payload: res.data });
          return res?.data;
        } else {
          throw res;
        }
      })
      .catch((error) => {
        throw error;
      });
  };

export const addSubscription = async (params, headers) => {
  return sendPostRequestWithHeaders({
    url: apiConstants.ADD_SUBSCRIPTION,
    body: params,
    headers,
  })
    .then((res) => {
      if (res.status) {
        return res;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const getActiveSubscription =
  (params, accessToken = null, isATV = false, err) =>
  async (dispatch) => {
    try {
      let url = apiConstants.ACTIVE_SUBSCRIPTION;
      const res = await postRequestAuthenticated(url, params, accessToken);
      if (res?.status) {
        let activeUserList = filterUserEntitlement(res?.data);
        if (isATV) {
          try {
            const storeFrontResponse = await dispatch(getStoreFrontProducts(true));
            if (storeFrontResponse?.data?.results) {
              const isValidForTV = checkTVPack(
                storeFrontResponse,
                activeUserList?.currentPlan?.skuCode
              );
              activeUserList = {
                ...activeUserList,
                isValidForTV,
              };
            }
          } catch (storeFrontError) {
            console.error("Error fetching storefront products:", storeFrontError);
          }
        }

        dispatch({ type: authConstants.USER_ENTITLEMENT, payload: activeUserList });
        return activeUserList;
      } else {
        err?.(res);
      }
      return res;
    } catch (error) {
      console.log(error);
      err?.(error);
    }
  };

export const thirdPartyLogin = async (
  params,
  deviceDetails,
  callback = () => {},
  err = () => {}
) => {
  try {
    let url = apiConstants.THIRD_PARTY_LOGIN;
    const res = await sendPostRequestWithDeviceDetails(url, params, deviceDetails);
    callback(res);
  } catch (error) {
    err(error);
  }
};

export const getContact = async (
  accessToken,
  callback = () => {},
  err = () => {},
  cp_id = null
) => {
  const URL = apiConstants.FETCH_ACCOUNT_DETAILS;
  {
    try {
      const res = await getRequestAuthenticated(URL, {}, accessToken, {}, null, cp_id);
      callback(res?.data);
    } catch (error) {
      err(error);
    }
  }
};

export const changeSubscription = async (params, headers) => {
  return sendPostRequestWithHeaders({
    url: apiConstants.CHANGE_SUBSCRIPTION,
    body: params,
    headers,
  })
    .then((res) => {
      if (res.status) {
        return res;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const fetchProrateAmount = async (payload) => {
  return postRequestAuthenticated(apiConstants.PRORATE_AMOUNT, payload)
    .then((res) => {
      if (res.status) {
        return res?.data;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const fetchCampaignDetails =
  (householdId, platformName = false, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    let url = apiConstants.HOUSE_HOLD(householdId, platformName);
    try {
      const res = await sendGetRequest(url);
      if (res?.status) {
        dispatch({
          type: authConstants.GET_HOUSE_HOLD,
          payload: res?.data,
        });
        callback?.(res?.data);
      } else {
        err(res);
      }
      return res;
    } catch (error) {
      err(error);
    }
  };

export const addSubscriptionMobile =
  (params, accessToken, verifierData = [], callback = () => {}, err = () => {}) =>
  async (dispatch, getState) => {
    const URL = apiConstants.ADD_SUBSCRIPTION;
    {
      try {
        const res = await postRequestAuthenticated(
          URL,
          params,
          accessToken,
          null,
          null,
          null,
          verifierData
        );
        callback && callback(res);
        let activeSubscriptionParams = {
          returnLiveEvents: true,
          cache: false,
        };
        dispatch(getActiveSubscription(activeSubscriptionParams, accessToken));
        return res;
      } catch (error) {
        err?.();
        return error;
      }
    }
  };

export const toggleLogoutModal =
  (payload = true) =>
  (dispatch) => {
    dispatch({ type: authConstants.SIGNOUT_MODAL_OPEN, payload: payload });
  };

export const userLoggedOut =
  (payload = true) =>
  (dispatch) => {
    dispatch({ type: authConstants.LOGGED_OUT_MODAL, payload: payload });
  };

export const setFavBanner = (payload) => async (dispatch) => {
  dispatch({
    type: authConstants.SET_FAV_BANNER,
    payload,
  });
};

export const clearFavBanner = (payload) => async (dispatch) => {
  dispatch({
    type: authConstants.CLEAR_FAV_BANNER,
    payload,
  });
};
export const clearApiErrorState = () => (dispatch) => {
  dispatch({ type: authConstants.CLEAR_API_ERROR });
};

export const cancelSubscription =
  (params, accessToken = null, onCallBack = () => {}) =>
  async (dispatch) => {
    try {
      let url = apiConstants.CANCEL_SUBSCRIPTION;
      const res = await deleteRequestAuthenticated(url, params, accessToken);
      if (res.status) {
        let activeSubscriptionParams = {
          returnLiveEvents: true,
          cache: false,
        };
        onCallBack(res);
        dispatch(getActiveSubscription(activeSubscriptionParams, accessToken));
      } else {
        onCallBack(res);
      }
    } catch (error) {
      onCallBack(error);
    }
  };

export const fetchTransactionHistory = async () => {
  return getRequestAuthenticated(apiConstants.GET_TRANSACTION_HISTORY)
    .then((res) => {
      if (res?.status) {
        return res?.data;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const postRequestWithHeaders = async ({ body, headers }) => {
  return sendPostRequestWithHeaders({
    url: apiConstants.AUTHORIZE_PAYMENT,
    body,
    headers,
  })
    .then((res) => {
      if (res.status) {
        return res?.data;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const updatePaymentInfo = (params) => () => {
  return postRequestAuthenticated(apiConstants.UPDATE_PAYMENT_INFO, params)
    .then((res) => {
      if (res?.status) {
        return res?.data;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};
export const setApiWaitRoomModal = (data) => {
  return {
    type: authConstants.Api_Waiting_Room_Modal,
    payload: data,
  };
};
export const setRefreshApi = (data) => {
  return {
    type: authConstants.Refresh_Api,
    payload: data,
  };
};

export const getTransactionHistory = (accessToken) => async (dispatch) => {
  const url = `${apiConstants.TRANSACTION_HISTORY}`;
  return new Promise((resolve, reject) => {
    getRequestAuthenticated(url, null, accessToken)
      .then((res) => {
        if (res?.status) {
          // Dispatch any necessary actions here if needed
          resolve(res);
        } else {
          reject(new Error("Failed to fetch transaction history"));
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};

export const getTransactionHistoryUpdated = (accessToken) => async (dispatch) => {
  const url = `${apiConstants.TRANSACTION_HISTORY_UPDATED}`;
  return new Promise((resolve, reject) => {
    getRequestAuthenticated(url, null, accessToken)
      .then((res) => {
        if (res?.status) {
          // Dispatch any necessary actions here if needed
          resolve(res);
        } else {
          reject(new Error("Failed to fetch transaction history"));
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};

export const fetchInvoiceFileData = (payload, accessToken) => {
  return postRequestAuthenticated(
    apiConstants.GET_PAYMENT_INVOICE,
    { paymentId: payload },
    accessToken
  );
};

export const getUserDetails = (accessToken, cpId) => async (dispatch) => {
  let url = `${apiConstants.UPDATE_PROFILE}`;
  try {
    const res = await getRequestAuthenticated(url, null, accessToken, {}, null, cpId);
    if (res?.status) {
      dispatch({
        type: authConstants.GET_USER_DETAILS,
        payload: res?.data,
      });
      return res.data;
    } else {
      console.error(res, "getUserDetails Api Failed");
    }
    return res;
  } catch (error) {
    console.error(error, "getUserDetails Api error");
  }
};

export const setTextLoader = (payload) => {
  return {
    type: authConstants.SET_TEXT_LOADER,
    payload,
  };
};

export const removeTextLoader = (payload) => {
  return {
    type: authConstants.REMOVE_TEXT_LOADER,
    payload,
  };
};

export const getLoginPage = async (nonce, state) => {
  let url = `${apiConstants.LOGIN_AUTHORIZE}?nonce=${nonce}&state=${state}`;
  return new Promise((resolve, reject) => {
    sendGetRequest(url)
      .then((res) => {
        resolve(res);
      })
      .catch((error) => {
        reject(error);
      });
  });
};

export const updateUserDeviceTimestamp = (params, accesssToken, deviceDetails) => () => {
  return patchRequestAuthenticated(
    apiConstants.UPDATE_USER_DEVICE_TIMESTAMP(params?.cpId, params?.deviceId, params?.timeStamp),
    {},
    accesssToken,
    "",
    deviceDetails
  )
    .then((res) => {
      console.log("updateUserDeviceTimestamp", res);

      if (res?.status) {
        return res?.data;
      } else {
        throw res;
      }
    })
    .catch((error) => {
      throw error;
    });
};

export const callBeatApi = (cp_id, device_id, accessToken, params = {}) => {
  const url = `${apiConstants.BEAT_API_SERVICE}/${cp_id}/device/${device_id}/${getCurrentTimestamp()}`;
  return new Promise((resolve, reject) => {
    sendPatchRequest(url, params, accessToken)
      .then((res) => {
        if (res?.status) {
          resolve(res);
        } else {
          reject(new Error("Failed to update timestamp data"));
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};

export const updateUserActivity = ({
  cpId,
  deviceId,
  timeStamp = Date.now(),
  accessToken,
  userProfileId,
}) => {
  try {
    const url = `${apiConstants.SET_USER_ACTIVITY}/${cpId}/device/${deviceId}/${timeStamp}`;
    return patchRequestAuthenticated(url, {}, accessToken, userProfileId);
  } catch (error) {
    console.error(error);
  }
};

export const updateUserActivitySmartTv = async (cp_id, device_id, accessToken, params = {}) => {
  try {
    const url = `${apiConstants.BEAT_API_SERVICE}/${cp_id}/device/${device_id}/${Date.now()}`;
    return sendPatchRequest(url, params, accessToken);
  } catch (error) {
    console.error(error);
  }
};

export const fetchWelcomeData = (id) => async (dispatch) => {
  try {
    const body = {
      headers: {
        ["platform_id"]: id,
      },
    };
    const url = `${apiConstants.WELCOME_SCREEN}`;
    const res = await sendGetRequest(url, body);
    if (res?.status) {
      dispatch({
        type: authConstants.WELCOME_DATA,
        payload: res?.data,
      });
      return;
    } else {
      err?.(res);
    }
  } catch (error) {
    err?.(error);
  }
};

export const updateRegisterInterest = (payload) => {
  return {
    type: authConstants.REGISTERED_INTEREEST,
    payload,
  };
};

export const fetchProfilePreferences =
  (callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      const res = await sendGetRequest("https://consumer-am.int.xp.irdeto.com/v1/lov/profile");
      if (res) {
        dispatch({
          type: authConstants.PROFILE_PREFERENCES,
          payload: res,
        });
        callback(res);
        return res;
      } else {
        err(res);
      }
    } catch (error) {
      console.error("Error fetching profile preferences:", error);
      err(error);
    }
  };

export const updateProfileAudioPreference =
  (profileId, audioPreferences, accessToken, callback = () => {}, err = () => {}) =>
  async (dispatch) => {
    try {
      console.log('======>')
      const url = `https://consumer-am.int.xp.irdeto.com/v1/profiles/${profileId}`;
      const body = {
        audioPreferences: audioPreferences
      };

      const res = await putRequestAuthenticated(url, body, accessToken);
      if (res && res.status) {
        callback(res);
        return res;
      } else {
        err(res);
      }
    } catch (error) {
      console.error("Error updating audio preferences:", error);
      err(error);
    }
  };


