import React from 'react';
import { View, Text } from 'react-native';
import styles from './styles';

const ProfilePreferenceLanguageItem = ({ item }) => {
  return (
    <View>
      <Text style={styles.itemName}>{item.name}</Text>
      {item.localName && item.localName !== item.name && (
        <Text style={styles.itemLocalName}>{item.localName}</Text>
      )}
    </View>
  );
};

export default ProfilePreferenceLanguageItem;
