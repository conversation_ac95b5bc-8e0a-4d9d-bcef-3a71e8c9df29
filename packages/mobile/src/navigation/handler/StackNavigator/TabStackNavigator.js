import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { COLORS } from '@video-ready/common/utils';
import useDeepLink from 'mobile/src/hooks/useDeepLink';
import Routenames from 'mobile/src/navigation/routeName';
import NoInternet from 'mobile/src/components/NoInternet';
import utils from 'mobile/src/utils';
import Splash from 'mobile/src/screens/Splash/Tab';
import GettingStarted from 'mobile/src/screens/GettingStarted/Tab';
import SSOLogin from 'mobile/src/screens/SSOLogin/Tab';
import SSOLogout from 'mobile/src/screens/SSOLogout';
import CONSTANTS from '@video-ready/common/utils/constant';
import BottomTabComponent from 'mobile/src/navigation/handler/BottomTabNavigator';
import DetailsPage from 'mobile/src/screens/ContentPage/Tab';
import VodPlayerScreen from '../../../screens/NewPlayer/VodPlayerScreen';
import RegisterInterest from 'mobile/src/screens/RegisterInterest/Tab';
import MoreItemsSection from 'mobile/src/screens/ViewAll';
import ProfileManagement from 'mobile/src/screens/ProfileManagement';
import UpdateProfile from 'mobile/src/screens/ProfileManagement/UpdateProfile';
import EditableProfileScreen from 'mobile/src/screens/ProfileManagement/EditableProfileScreen';
import ProfilePreferenceSetting from 'mobile/src/screens/ProfileManagement/ProfilePreferences/ProfilePreferenceSetting';
import ProfilePreferenceDynamic from 'mobile/src/screens/ProfileManagement/ProfilePreferences/ProfilePreferenceDynamic';
import ProfileAudioLanguage from 'mobile/src/screens/ProfileManagement/ProfilePreferences/ProfileAudioLanguage';
import ProfileStreamingQuality from 'mobile/src/screens/ProfileManagement/ProfilePreferences/ProfileStreamingQuality';
import ProfileSubtitleLanguage from 'mobile/src/screens/ProfileManagement/ProfilePreferences/ProfileSubtitleLanguage';
import CreateProfile from 'mobile/src/screens/ProfileManagement/CreateProfile';
import AvatarScreen from 'mobile/src/screens/ProfileManagement/AvatarScreen';
import OfflineDownloadProvider from '../../../downloadplayer/progressprovider/DownloadOfflineProvider';

const Stack = createNativeStackNavigator();

const TabStackNavigator = (props) => {
  useDeepLink();

  return (
    <React.Fragment>
      <OfflineDownloadProvider>
        <NoInternet />
        <Stack.Navigator
          initialRouteName={Routenames.SPLASH}
          screenOptions={{
            headerShown: false,
            ...(utils.isAndroid && { animation: 'none' }),
            statusBarStyle: 'light',
            statusBarColor: COLORS.TRANSPARENT,
            statusBarTranslucent: true,
            animation: 'none',
            stackPresentation: 'transparentModal',
            orientation: CONSTANTS.LANDSCAPE,
          }}
        >
          <Stack.Screen
            name={Routenames.SPLASH}
            component={Splash}
            options={{
              orientation: CONSTANTS.LANDSCAPE,
            }}
          />
          <Stack.Screen
            name={Routenames.GETTING_STARTED}
            component={GettingStarted}
            options={{
              orientation: CONSTANTS.LANDSCAPE,
            }}
          />
          <Stack.Screen
            component={SSOLogin}
            name={Routenames.SSO_LOGIN}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={SSOLogout}
            name={Routenames.SSO_LOGOUT}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={RegisterInterest}
            name={Routenames.REGISTER_INTEREST}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={BottomTabComponent}
            initialParams={props?.route?.params}
            name={Routenames.HOME_SCREEN}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={DetailsPage}
            name={Routenames.DETAILS_PAGE}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            name={Routenames.MORE_ITEMS_SECTION}
            component={MoreItemsSection}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={VodPlayerScreen}
            name={Routenames.FULLSCREEN_NEW_PLAYER}
            options={{
              ...(utils.isAndroid && { statusBarHidden: true }),
              animation: 'none',
              orientation: CONSTANTS.LANDSCAPE,
              autoHideHomeIndicator: true,
            }}
          />
          <Stack.Screen
            component={ProfileManagement}
            name={Routenames.PROFILE_MANAGEMENT}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={UpdateProfile}
            name={Routenames.UPDATE_PROFILE}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={EditableProfileScreen}
            name={Routenames.EDITABLE_PROFILE_SCREEN}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={ProfilePreferenceSetting}
            name={Routenames.PROFILE_PREFERENCE_SETTING}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={ProfilePreferenceDynamic}
            name={Routenames.PROFILE_PREFERENCE_DYNAMIC}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={ProfileAudioLanguage}
            name={Routenames.PROFILE_AUDIO_LANGUAGE}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={ProfileStreamingQuality}
            name={Routenames.PROFILE_STREAMING_QUALITY}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={ProfileSubtitleLanguage}
            name={Routenames.PROFILE_SUBTITLE_LANGUAGE}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={CreateProfile}
            name={Routenames.CREATE_PROFILE}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
          <Stack.Screen
            component={AvatarScreen}
            name={Routenames.AVATAR_SCREEN}
            options={{ orientation: CONSTANTS.LANDSCAPE }}
          />
        </Stack.Navigator>
      </OfflineDownloadProvider>
    </React.Fragment>
  );
};

export default TabStackNavigator;
