import {
  View,
  Text,
  StyleSheet,
  NativeModules,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import React, { useMemo } from 'react';

import i18next from 'i18next';
import { SvgUri } from 'react-native-svg';
import { useSelector } from 'react-redux';
import {
  autocaptureNavigationTrack,
  withReactNavigationAutotrack,
} from '@convivainc/conviva-react-native-appanalytics';
import { BlurView } from '@react-native-community/blur';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import {
  normalizeFont,
  normalizeWidth,
  normalizeHeight,
} from 'mobile/src/styles/Mixins';
import Splash from 'mobile/src/screens/Splash';
import Player from 'mobile/src/screens/Player';
import Browser from 'mobile/src/screens/Browser';
import CMSHome from 'mobile/src/screens/CMSHome';
import SSOLogin from 'mobile/src/screens/SSOLogin';
import SSOLogout from 'mobile/src/screens/SSOLogout';
import OtpScreen from 'mobile/src/screens/otpScreen';
import MyAccount from 'mobile/src/screens/MyAccount';
import MyDevices from 'mobile/src/screens/MyDevices';
import MyProfile from 'mobile/src/screens/MyProfile';
import Favourites from 'mobile/src/screens/Favourites';
import ProfileScreen from 'mobile/src/screens/Profile';
import useDeepLink from 'mobile/src/hooks/useDeepLink';
import SmartLogin from 'mobile/src/screens/SmartLogin';
import HomeScreen from 'mobile/src/screens/HomeScreen';
import appConfig from '@video-ready/common/utils/config';
import WaitingRoom from 'mobile/src/screens/WaitingRoom';
import DetailsPage from 'mobile/src/screens/ContentPage';
import Routenames from 'mobile/src/navigation/routeName';
import Settings from 'mobile/src/screens/Settings/index';
import { CustomDrawerContent } from '../DrawerNavigator';
import NoInternet from 'mobile/src/components/NoInternet';
import { COLORS, FONTS } from '@video-ready/common/utils';
import MoreItemsSection from 'mobile/src/screens/ViewAll';
import CONSTANTS from '@video-ready/common/utils/constant';
import SignUpScreen from 'mobile/src/screens/SignUpScreen';
import SearchPageView from 'mobile/src/screens/SearchPage';
import utils, { getFullImagePath, ScreenWidth } from 'mobile/src/utils';
import Notifications from 'mobile/src/screens/Notifications';
import ManageDevices from 'mobile/src/screens/ManageDevices';
import CustomTabBar from 'mobile/src/components/CustomTabBar';
import GettingStarted from 'mobile/src/screens/GettingStarted';
import LoginComponent from 'mobile/src/screens/LoginComponent';
import SeriesEpisodes from 'mobile/src/screens/SeriesEpisodes';
import SportScreen from 'mobile/src/screens/HomeScreen/Sports';
import AccountDetails from 'mobile/src/screens/AccountDetails';
import SmartTvLinking from 'mobile/src/screens/SmartTvLinking';
import LinkingSuccessScreen from 'mobile/src/screens/SmartTvLinking/LinkingSuccessScreen';
import LiveContentPage from 'mobile/src/screens/LiveContentPage';
import SignInComponent from 'mobile/src/screens/SignInComponent';
import DrawerHeader from 'mobile/src/components/NavigationHeader';
import SubscriptionPlan from 'mobile/src/screens/SubscriptionPlan';
import LanguageSettings from 'mobile/src/screens/LanguageSettings';
import { ThemeStore } from '@video-ready/common/store/themeReducer';
import NavigationHeader from 'mobile/src/navigation/NaviationHeader';
import TvLinkCode from 'mobile/src/screens/SmartTvLinking/TvLinkCode';
import OtpSuccessScreen from 'mobile/src/screens/otpScreen/OtpSuccess';
import { Profile_New } from '@video-ready/common/utils/imageConstants';
import EPGScreen from 'mobile/src/screens/FullEPG/EPGScreen';
import TransactionHistory from 'mobile/src/screens/TransactionHistory';
import CreateProfileScreen from 'mobile/src/screens/CreateProfileScreen';
import BillingHistory from 'mobile/src/screens/MyAccount/BillingHistory';
import UpdateScreen from 'mobile/src/screens/AccountDetails/UpdateScreen';
import VodPlayerScreen from 'mobile/src/screens/NewPlayer/VodPlayerScreen';
import MyLibrary from 'mobile/src/screens/MyLibrary';
import useLocalReminderSetup from 'mobile/src/hooks/useLocalReminderSetup';
import LanguageList from 'mobile/src/screens/LanguageSettings/LanguageList';
import DeviceManagement from 'mobile/src/screens/MyAccount/DeviceManagement';
import OfflinePlayer from 'mobile/src/screens/MyLibrary/components/OfflinePlayer';
import RegisterInterest from '../../../screens/RegisterInterest';
import AppGradient from '../../../components/AppBackground';
import { useSafeHeights } from 'mobile/src/hooks/useSafeHeights';
import RoundButton from '../../../components/RoundButton';
import MOBILE_GRADIENT_BUTTON_TYPE from '../../../components/ButtonBorderGradient/mobileGradientButtonType';
import ProfileManagement from 'mobile/src/screens/ProfileManagement';
import UpdateProfile from 'mobile/src/screens/ProfileManagement/UpdateProfile';
import EditableProfileScreen from 'mobile/src/screens/ProfileManagement/EditableProfileScreen';
import ProfilePreferenceSetting from 'mobile/src/screens/ProfileManagement/ProfilePreferences/ProfilePreferenceSetting';
import ProfilePreferenceDynamic from 'mobile/src/screens/ProfileManagement/ProfilePreferences/ProfilePreferenceDynamic';
import ProfileAudioLanguage from 'mobile/src/screens/ProfileManagement/ProfilePreferences/ProfileAudioLanguage';
import ProfileStreamingQuality from 'mobile/src/screens/ProfileManagement/ProfilePreferences/ProfileStreamingQuality';
import ProfileSubtitleLanguage from 'mobile/src/screens/ProfileManagement/ProfilePreferences/ProfileSubtitleLanguage';
import CreateProfile from 'mobile/src/screens/ProfileManagement/CreateProfile';
import AvatarScreen from 'mobile/src/screens/ProfileManagement/AvatarScreen';
import OfflineDownloadProvider from '../../../downloadplayer/progressprovider/DownloadOfflineProvider';
import DownloadedEpisodesPage from '../../../screens/MyLibrary/components/Downloads/DownloadedEpisodesPage';

const Tab = createBottomTabNavigator();
const Drawer = createDrawerNavigator();
const Stack = createNativeStackNavigator();

const MobileStackNavigator = () => {
  const { t: translate } = i18next;
  useDeepLink();

  const globalState = React.useContext(ThemeStore);
  const { themeState } = globalState;
  const { backgroundColor, primaryColor } = themeState?.themes;

  const preferredOrientation = utils.isTab ? 'landscape' : 'portrait';
  const menuDataList = useSelector((state) => state?.auth?.menuDataList);
  const appLoader = useSelector((state) => state.miscellaneous.appLoaderMobile);
  const isWatingRoomEnabled = useSelector(
    (state) => state.auth.isWatingRoomEnabled
  );

  const menuDataListNew = useMemo(() => {
    const newItem = {
      id: '67e568d4a38e9b746e5f32a7s',
      active: true,
      menuType: 'MY_LIBRARY',
      tabName: 'My Library',
      linkToPage: '667cff30f9de7308d649fedc',
      seoTags: null,
      placementType: ['TAB'],
      profileType: 'GUEST',
      hoverMenuIcon: 'menu-icon/1742806415031-Guide.png',
      position: 1,
      menuIcon: 'menu-icon/1742888770825-Guide.png',
      parentMenuId: null,
      defaultTabName: 'MY Library',
      platformId: '5f449b743d24a0435dbdb429',
      default: false,
    };

    return [...menuDataList, newItem];
  }, [menuDataList]);

  let filterTab =
    menuDataListNew?.length > 0 &&
    menuDataListNew
      .sort((a, b) => (a.position > b.position ? 1 : -1))
      ?.filter((item) => item.placementType.includes('TAB'));

  const filterDrawer =
    menuDataListNew?.length > 0 &&
    menuDataListNew?.filter((item) => {
      item.placementType.includes('HAMBURGER');
    });

  const filterEditorialContent =
    menuDataListNew?.length > 0 &&
    menuDataListNew?.filter((item) => item.menuType.includes('EDITORIAL'));

  const filterReelContent =
    menuDataListNew?.length > 0 &&
    menuDataListNew?.filter((item) => item.menuType.includes('REEL'));

  const filterCMSContent =
    menuDataListNew?.length > 0 &&
    menuDataListNew?.filter((item) => item.menuType.includes('CMS'));

  const defaultTab = useMemo(() => {
    if (menuDataListNew?.length > 0) {
      return (
        menuDataListNew?.find((item) => item?.default) || menuDataListNew[0]
      );
    }
  }, [menuDataListNew]);

  const onPressRetryButton = () => {
    NativeModules.DevSettings.reload();
  };

  const transitionEffectWithHeader = (
    title,
    isBackButtonNeeded,
    gradientNeeded
  ) => ({
    headerShown: true,
    orientation: preferredOrientation,
    header: (props) => {
      return (
        <DrawerHeader
          title={title}
          isBackButtonNeeded={isBackButtonNeeded}
          gradientNeeded={gradientNeeded}
        />
      );
    },
  });

  const BottomTab = () => {
    const { safeEdges } = useSafeHeights();
    const bottomTabBarVisible_mobile = useSelector(
      (state) => state?.home?.bottomTabBarVisible_mobile
    );
    let index = 0;
    if (appLoader) {
      return (
        <View style={[styles.container]}>
          <View style={styles.appGradient}>
            <AppGradient />
          </View>
          <ActivityIndicator size={'large'} color={COLORS.COLORS_EA0080} />
          <View style={styles.splashStatusBar(safeEdges.top)} />
        </View>
      );
    } else if (!menuDataListNew?.length) {
      return (
        <View style={styles.container}>
          <View style={styles.appGradient}>
            <AppGradient />
          </View>
          <Text style={styles.containerText}>{translate('oops')}</Text>
          <Text style={styles.containerText}>
            {translate('something_went_wrong')}
          </Text>
          <RoundButton
            onPress={onPressRetryButton}
            text={translate('retry')}
            style={styles.retryButton}
            textStyle={styles.retryButtonText}
            showSelected={true}
            buttonType={MOBILE_GRADIENT_BUTTON_TYPE.LARGE}
          />
          <View style={styles.splashStatusBar(safeEdges.top)} />
        </View>
      );
    }

    const renderCustomTab = (props) => (
      <CustomTabBar defaultTab={defaultTab} {...props} />
    );

    const renderTabScreen = (item, index) => {
      switch (item?.menuType) {
        case CONSTANTS.MENU_TYPE.TAB:
          return (
            <Tab.Screen
              name={`${item.id}`}
              key={index.toString()}
              component={HomeScreen}
              initialParams={{ ...item, filterTab }}
            />
          );
        case CONSTANTS.MENU_TYPE.SETTINGS:
          return (
            <Tab.Screen
              name={`${item.id}`}
              key={index.toString()}
              component={ProfileScreen}
              initialParams={{ ...item, filterTab }}
            />
          );
        case CONSTANTS.MENU_TYPE.SEARCH:
          return (
            <Tab.Screen
              name={`${item.id}`}
              key={index.toString()}
              component={SearchPageView}
              initialParams={{ ...item, filterTab }}
            />
          );
        case CONSTANTS.MENU_TYPE.TV_GUIDE:
          return (
            <Tab.Screen
              name={`${item.id}`}
              key={index.toString()}
              component={EPGScreen}
              initialParams={{ ...item, filterTab }}
              options={{
                unmountOnBlur: true,
              }}
            />
          );
        case CONSTANTS.MENU_TYPE.MY_LIBRARY:
          return (
            <Tab.Screen
              name={`${item.id}`}
              key={index.toString()}
              component={MyLibrary}
              initialParams={{ ...item, filterTab }}
            />
          );
        default:
          return;
      }
    };

    return (
      <Tab.Navigator
        tabBar={renderCustomTab}
        initialRouteName={defaultTab?.id}
        backBehavior='initialRoute'
        screenOptions={({ route, navigation }) => ({
          header: (props) => (
            <NavigationHeader
              route={route}
              showHeader={Boolean(
                route?.params?.menuType === CONSTANTS.MENU_TYPE.TAB
              )}
              navigation={navigation}
              {...props}
            />
          ),
          headerTransparent: true,
          headerLargeStyle: {},
          tabBarIconStyle: {
            marginTop: normalizeHeight(10),
          },
          tabBarLabelStyle: {
            marginBottom: normalizeHeight(5),
          },
          tabBarStyle: {
            position: 'absolute',
            ...(utils.isTab && {
              height: utils.isAndroid
                ? normalizeHeight(100)
                : normalizeHeight(120),
            }),
            backgroundColor: COLORS.COLORS_390561CC,
            elevation: 5,
            shadowColor: COLORS.COLOR_000000CC,
            shadowOpacity: 2,
            shadowOffset: {
              height: 5,
            },
            shadowRadius: 5,
          },
          tabBarLabel: ({ focused }) => {
            let Icon = filterTab.find(
              (item) => item?.id?.toString() === route?.name
            );
            return (
              <View key={index.toString()}>
                <Text
                  numberOfLines={1}
                  style={{
                    ...styles?.tabItemText,
                    color: focused ? COLORS.COLOR_2680EB : COLORS.COLOR_FFFFFF,
                  }}
                >
                  {Icon?.tabName?.trim() ?? ''}
                </Text>
              </View>
            );
          },
          tabBarIcon: ({ focused }) => {
            let Icon = filterTab.find(
              (item) => item?.id?.toString() === route?.name
            );
            if (route?.params?.tabName === 'Profile') {
              return <Profile_New />;
            }
            return (
              <View key={index.toString()} style={styles.svgTabStyle}>
                <SvgUri
                  fill={
                    focused
                      ? (primaryColor ?? COLORS.COLOR_2680EB)
                      : COLORS.COLOR_FFFFFF
                  }
                  width={utils.isTab ? normalizeWidth(22) : normalizeWidth(20)}
                  height={
                    utils.isTab ? normalizeWidth(22) : normalizeHeight(20)
                  }
                  uri={getFullImagePath(Icon.menuIcon)}
                />
              </View>
            );
          },
          tabBarLabelPosition: 'below-icon',
          tabBarBackground: () =>
            utils.isIOS ? (
              <BlurView
                overlayColor={COLORS.TRANSPARENT}
                reducedTransparencyFallbackColor={COLORS.TRANSPARENT}
                blurType={'extraDark'}
                style={StyleSheet.absoluteFill}
              />
            ) : (
              <View
                style={{
                  backgroundColor: COLORS.COLOR_BLACK_OPACITY7,
                  ...StyleSheet.absoluteFill,
                }}
              />
            ),
          tabBarShowLabel: true,
          headerShown: bottomTabBarVisible_mobile ? true : false,
        })}
      >
        {menuDataListNew.length > 0 &&
          menuDataListNew.map((item, index) => renderTabScreen(item, index))}
      </Tab.Navigator>
    );
  };

  const MobileDrawerNav = (props) => {
    return (
      <Drawer.Navigator
        screenOptions={({ navigation, route }) => ({
          swipeEnabled: false,
          headerShown: false,
          drawerType: 'front',
          drawerStyle: {
            backgroundColor: COLORS.TRANSPARENT,
            ...(utils?.isTab && { width: '55%' }),
          },
        })}
        drawerContent={(props) => (
          <CustomDrawerContent
            {...props}
            filterDrawer={filterDrawer}
            filterEditorialContent={filterEditorialContent}
            filterCMSContent={filterCMSContent}
            filterReelContent={filterReelContent}
          />
        )}
      >
        <Drawer.Screen
          name={'HomeDrawer'}
          component={BottomTab}
          initialParams={props?.route?.params}
        />
      </Drawer.Navigator>
    );
  };

  return (
    <React.Fragment>
      <OfflineDownloadProvider>
        <NoInternet />
        <Stack.Navigator
          initialRouteName={Routenames.SPLASH}
          screenOptions={{
            headerShown: false,
            ...(utils.isAndroid && { animation: 'none' }),
            statusBarStyle: 'light',
            statusBarColor: COLORS.TRANSPARENT,
            statusBarTranslucent: true,
            animation: 'none', // Use 'fade' animation for the stack
            stackPresentation: 'transparentModal',
            ...(utils.isTab && { orientation: 'landscape' }),
          }}
        >
          <Stack.Screen
            name={Routenames.SPLASH}
            component={Splash}
            options={{
              orientation: preferredOrientation,
            }}
          />

          <Stack.Screen
            name={Routenames.GETTING_STARTED}
            component={GettingStarted}
            options={{
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.LOGIN}
            component={LoginComponent}
            options={{
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            component={SSOLogout}
            name={Routenames.SSO_LOGOUT}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            name={Routenames.SPORT_SCREEN}
            component={SportScreen}
            options={{
              ...transitionEffectWithHeader('Sports', true),
            }}
            initialParams={{ filterTab }}
          />
          <Stack.Screen
            name={Routenames.SIGN_UP}
            component={SignUpScreen}
            options={{
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.OTP}
            component={OtpScreen}
            options={{
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            component={DetailsPage}
            name={Routenames.DETAILS_PAGE}
            initialParams={{ filterTab }}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={DownloadedEpisodesPage}
            name={Routenames.DOWNLOADED_EPISODE_PAGE}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={SearchPageView}
            initialParams={{ filterTab }}
            name={Routenames.SEARCH_PAGE}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            name={Routenames.OTP_SUCCESS}
            component={OtpSuccessScreen}
            options={{
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.NOTIFICATIONS}
            component={Notifications}
            options={{
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.MORE_ITEMS_SECTION}
            component={MoreItemsSection}
            options={{ orientation: preferredOrientation }}
            initialParams={{ filterTab }}
          />
          <Stack.Screen
            component={Player}
            name={Routenames.Player}
            options={{
              animation: 'none',
              orientation: 'landscape',
              autoHideHomeIndicator: true,
              ...(utils.isAndroid && { statusBarHidden: true }),
            }}
          />
          {!isWatingRoomEnabled && (
            <Stack.Screen
              component={MobileDrawerNav}
              name={Routenames.HOME_SCREEN}
              initialParams={{ filterTab }}
              options={{
                ...(utils.isAndroid && { orientation: preferredOrientation }),
              }}
            />
          )}
          <Stack.Screen
            component={SignInComponent}
            name={Routenames.SIGN_IN_COMPONENT}
            options={({ route }) => ({
              orientation: preferredOrientation,
              animation: utils.isTab
                ? 'fade'
                : route.params?.animation || 'slide_from_bottom',
            })}
          />
          <Stack.Screen
            name={Routenames.CMSHome}
            component={CMSHome}
            options={{
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            component={Browser}
            name={Routenames.BROWSER}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            name={Routenames.Settings}
            component={Settings}
            options={{
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.MYDEVICES}
            component={MyDevices}
            options={{
              ...transitionEffectWithHeader('Devices', false),
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.MYACCOUNT}
            component={MyAccount}
            options={{
              ...transitionEffectWithHeader('My Account', false),
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.MYPROFILE}
            component={MyProfile}
            options={{
              ...transitionEffectWithHeader('Profiles', false),
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            component={AccountDetails}
            name={Routenames.ACCOUNT_DETAIL}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            name={Routenames.DEVICEMANAGEMENT}
            component={DeviceManagement}
            options={{
              ...transitionEffectWithHeader('Device Management', false),
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.BILLINGHISTORY}
            component={BillingHistory}
            options={{
              ...transitionEffectWithHeader('Billing History', false),
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.FAVOURITES}
            component={Favourites}
            initialParams={{ filterTab }}
            options={{
              ...transitionEffectWithHeader('My Favourites', false),
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.SMARTLOGIN}
            component={SmartLogin}
            options={{
              ...transitionEffectWithHeader(
                'Smart Tv Quick Login',
                false,
                false
              ),
              orientation: preferredOrientation,
            }}
          />
          <Stack.Screen
            name={Routenames.SERIES_EPISODES}
            component={SeriesEpisodes}
            options={({ route }) => {
              return {
                ...(utils.isIOS && {
                  presentation: 'modal',
                  animation: 'slide_from_bottom',
                }),
                headerShown: true,
                orientation: preferredOrientation,

                ...transitionEffectWithHeader(route || '', false),
              };
            }}
          />
          <Stack.Screen
            component={VodPlayerScreen}
            name={Routenames.FULLSCREEN_NEW_PLAYER}
            options={{
              ...(utils.isAndroid && { statusBarHidden: true }),
              animation: 'none',
              orientation: 'landscape',
              autoHideHomeIndicator: true,
            }}
          />
          <Stack.Screen
            component={ManageDevices}
            name={Routenames.MANAGE_DEVICES}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={LanguageSettings}
            name={Routenames.LANGUAGE_SETTINGS}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={LanguageList}
            name={Routenames.LANGUAGE_LIST}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            name={Routenames.SUBSCRIPTION_PLAN}
            component={SubscriptionPlan}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={UpdateScreen}
            name={Routenames.UPDATE_SCREEN}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={EPGScreen}
            name={Routenames.EPGScreen}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={TransactionHistory}
            name={Routenames.TRANSACTION_HISTORY}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={WaitingRoom}
            name={Routenames.WAITING_ROOM}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={SSOLogin}
            name={Routenames.SSO_LOGIN}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={SmartTvLinking}
            name={Routenames.SMART_TV_LINKING}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={LinkingSuccessScreen}
            name={Routenames.LINKING_SUCCESS_SCREEN}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={TvLinkCode}
            name={Routenames.TV_LINK_CODE}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={ProfileScreen}
            name={Routenames.PROFILE_SCREEN}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={RegisterInterest}
            name={Routenames.REGISTER_INTEREST}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={ProfileManagement}
            name={Routenames.PROFILE_MANAGEMENT}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={UpdateProfile}
            name={Routenames.UPDATE_PROFILE}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={EditableProfileScreen}
            name={Routenames.EDITABLE_PROFILE_SCREEN}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={ProfilePreferenceSetting}
            name={Routenames.PROFILE_PREFERENCE_SETTING}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={ProfilePreferenceDynamic}
            name={Routenames.PROFILE_PREFERENCE_DYNAMIC}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={ProfileSubtitleLanguage}
            name={Routenames.PROFILE_SUBTITLE_LANGUAGE}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={ProfileAudioLanguage}
            name={Routenames.PROFILE_AUDIO_LANGUAGE}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={ProfileStreamingQuality}
            name={Routenames.PROFILE_STREAMING_QUALITY}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={CreateProfile}
            name={Routenames.CREATE_PROFILE}
            options={{ orientation: preferredOrientation }}
          />
          <Stack.Screen
            component={AvatarScreen}
            name={Routenames.AVATAR_SCREEN}
            options={{ orientation: preferredOrientation }}
          />
        </Stack.Navigator>
      </OfflineDownloadProvider>
    </React.Fragment>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  containerText: {
    color: COLORS.COLOR_FFFFFF,
    fontSize: normalizeFont(15),
    fontFamily: FONTS.ROBOTO_REGULAR,
  },
  tabItemText: {
    textAlign: 'center',
    fontFamily: FONTS.ROBOTO_REGULAR,
    paddingHorizontal: normalizeWidth(2),
    fontSize: utils.isTab ? normalizeFont(14) : normalizeFont(12),
    marginTop: utils.isTab ? normalizeHeight(7) : normalizeHeight(3),
  },
  svgTabStyle: {
    marginBottom: normalizeHeight(5),
  },
  appGradient: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  splashStatusBar: (statusBar) => ({
    top: 0,
    left: 0,
    right: 0,
    height: statusBar,
    position: 'absolute',
    backgroundColor: COLORS.COLOR_0000001A,
    zIndex: 1,
  }),
  retryButton: {
    width: ScreenWidth - normalizeWidth(24),
    height: normalizeHeight(44),
    marginTop: normalizeWidth(32),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  retryButtonText: {
    color: COLORS.COLOR_EBEBEB,
    fontFamily: FONTS.MULISH_BOLD,
    fontSize: normalizeFont(16),
    marginBottom: normalizeHeight(2),
  },
});

export default withReactNavigationAutotrack(autocaptureNavigationTrack)(
  MobileStackNavigator
);
