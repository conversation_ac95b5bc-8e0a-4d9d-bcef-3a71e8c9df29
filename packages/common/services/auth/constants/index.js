export const authConstants = {
  LOGOUT: "LOGOUT",
  TENANT_INFO: "TENANT_INFO",
  SET_CONFIG_INFO: "SET_CONFIG_INFO",
  SET_SIGN_IN_DATA: "SET_SIGN_IN_DATA",
  OPERATOR_COLOR_CODES: "OPERATOR_COLOR_CODES",
  SET_OTP_RES_DATA: "SET_OTP_RES_DATA",
  GET_QR_CODE_IMAGES: "GET_QR_CODE_IMAGES",
  GET_RAIL_CART_SIZE: "GET_RAIL_CART_SIZE",
  GET_MENU_DATA_LIST: "GET_MENU_DATA_LIST",
  SET_USER_ENTITLEMENTS: "SET_USER_ENTITLEMENTS",
  IS_DEEP_LINK_TRIGGERED: "IS_DEEP_LINK_TRIGGERED",
  SET_CONTINUE_WATCHING_DATA: "SET_CONTINUE_WATCHING_DATA",
  UPDATE_CONTINUE_WATCHING_DATA: "UPDATE_CONTINUE_WATCHING_DATA",
  GET_SEARCH_PAGE_DATA: "GET_SEARCH_PAGE_DATA",
  SET_CONTINUE_WATCHING_CONFIG_DATA: "SET_CONTINUE_WATCHING_CONFIG_DATA",
  TENANT_USER_INFO: "TENANT_USER_INFO",
  SET_APP_UPGRADE_DATA: "SET_APP_UPGRADE_DATA",
  INACTIVE_PLAYBACK: "INACTIVE_PLAYBACK",
  SET_VIDEO_INITIAL_BITRATE: "SET_VIDEO_INITIAL_BITRATE",
  GET_SEARCH_FILTER: "GET_SEARCH_FILTER",
  CLEAR_SEARCH: "CLEAR_SEARCH",
  CLEAR_API_ERROR: "CLEAR_API_ERROR",
  LOGIN_MODAL_DATA: "LOGIN_MODAL_DATA",
  DRM_CERTIFICATE_INFO: "DRM_CERTIFICATE_INFO",
  PLATFORM_ID: "PLATFORM_ID",
  RECOMMENDATION_ID: "RECOMMENDATION_ID",
  HOME_SCREEN_FOOTER: "HOME_SCREEN_FOOTER",
  TTL_TOKEN: "TTL_TOKEN",
  USER_ENTITLEMENT: "USER_ENTITLEMENT",
  PRODUCT_LIST: "PRODUCT_LIST",
  CSAI_INFO: "CSAI_INFO",
  NAME_UPDATE_MODAL_DATA: "NAME_UPDATE_MODAL_DATA",
  EMAIL_UPDATE_MODAL_DATA: "EMAIL_UPDATE_MODAL_DATA",
  MOBILE_UPDATE_MODAL_DATA: "MOBILE_UPDATE_MODAL_DATA",
  Api_Error_Modal: "Api_Error_Modal",
  IS_AUTHENTICATED: "IS_AUTHENTICATED",
  SUBSCRIPTION_INFO: "SUBSCRIPTION_INFO",
  GET_HOUSE_HOLD: "GET_HOUSE_HOLD",
  SIGNOUT_MODAL_OPEN: "SIGNOUT_MODAL_OPEN",
  LOGGED_OUT_MODAL: "LOGGED_OUT_MODAL",
  SET_FAV_BANNER: "SET_FAV_BANNER",
  CLEAR_FAV_BANNER: "CLEAR_FAV_BANNER",
  AUDIO_SUBTITLE_LIST: "AUDIO_SUBTITLE_LIST",
  LANGUAGE_SETTINGS: "LANGUAGE_SETTINGS",
  SET_REMINDER_CONTENT: "ADD_REMINDER_CONTENT",
  REMOVE_REMINDER_CONTENT: "REMOVE_REMINDER_CONTENT",
  Api_Waiting_Room_Modal: "Api_Waiting_Room_Modal",
  Refresh_Api: "Refresh_Api",
  PRICE_BASED_UPGRADE_DOWNGRADE: "PRICE_BASED_UPGRADE_DOWNGRADE",
  ROOT_CHECK: "ROOT_CHECK",
  IMAGEHEIGHTR: "IMAGEHEIGHTR",
  IMAGEWIDTHS: "IMAGEWIDTHS",
  DOB_UPDATE_MODAL_DATA: "DOB_UPDATE_MODAL_DATA",
  GET_USER_DETAILS: "GET_USER_DETAILS",
  SET_TEXT_LOADER: "SET_TEXT_LOADER",
  REMOVE_TEXT_LOADER: "REMOVE_TEXT_LOADER",
  RESOLUTION_CONFIG: "RESOLUTION_CONFIG",
  TV_PAIR_DATA: "TV_PAIR_DATA",
  IS_WAITING_ROOM_ENABLED: "IS_WAITING_ROOM_ENABLED",
  MAXIS_USER_UPDATE: "MAXIS_USER_UPDATE",
  WELCOME_DATA: "WELCOME_DATA",
  REGISTERED_INTEREEST: "REGISTERED_INTEREEST",
  PROFILE_PREFERENCES: "PROFILE_PREFERENCES",
};

export const GRANT_TYPES = {
  AUTHORIZATION_CODE: "urn:ietf:params:oauth:grant-type:authorization_code",
  TOKEN_EXCHANGE: "urn:ietf:params:oauth:grant-type:token_exchange",
  REFRESH_TOKEN: "urn:ietf:params:oauth:grant-type:refresh_token",
};
