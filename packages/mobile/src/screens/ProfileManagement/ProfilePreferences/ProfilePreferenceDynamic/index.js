import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useRoute } from '@react-navigation/native';
import useTranslate from 'mobile/src/hooks/useTranslate';
import useAuth from 'mobile/src/hooks/useAuth';
import ProfilePreferenceList from '../../Components/ProfilePreferenceList';

import { updateProfileAudioPreference } from '@video-ready/common/services/auth/action';
import utils from 'mobile/src/utils/index';
import { normalizeHeight } from 'mobile/src/styles/Mixins';

const PREFERENCE_TYPES = {
  SUBTITLE: 'subtitle',
  AUDIO: 'audio',
  STREAMING_QUALITY: 'streaming_quality',
};

const ProfilePreferenceDynamic = () => {
  const { getText } = useTranslate();
  const dispatch = useDispatch();
  const route = useRoute();
  const { profileId, accessToken } = useAuth();

  // Get preference type from route params
  const preferenceType = route.params?.type;

  // Subscribe to relevant data from Redux based on preference type
  const subtitleLanguagesFromAPI = useSelector(
    (state) => state.auth.profilePreferences?.subtitleLanguages || []
  );
  const audioLanguagesFromAPI = useSelector(
    (state) => state.auth.profilePreferences?.audioLanguages || []
  );
  const videoResolutionsFromAPI = useSelector(
    (state) => state.auth.profilePreferences?.videoResolutions || []
  );

  const [selectedValue, setSelectedValue] = useState('');
  const [data, setData] = useState([]);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    switch (preferenceType) {
      case PREFERENCE_TYPES.SUBTITLE:
        handleSubtitleData();
        break;
      case PREFERENCE_TYPES.AUDIO:
        handleAudioData();
        break;
      case PREFERENCE_TYPES.STREAMING_QUALITY:
        handleStreamingQualityData();
        break;
      default:
        console.warn('Unknown preference type:', preferenceType);
    }
  }, [preferenceType, subtitleLanguagesFromAPI, audioLanguagesFromAPI, videoResolutionsFromAPI]);

  const handleSubtitleData = () => {
    if (subtitleLanguagesFromAPI?.length) {
      const transformedLanguages = subtitleLanguagesFromAPI.map((lang) => ({
        id: lang.languageId,
        name: lang.name,
        localName: lang.localName,
        codes: lang.codes,
        selected: lang.languageId === 'en',
      }));

      transformedLanguages.push({
        id: 'off',
        name: 'Off',
        localName: 'Off',
        codes: [],
        selected: false,
      });

      setData(transformedLanguages);
      setSelectedValue('English');
    }
  };

  const handleAudioData = () => {
    if (audioLanguagesFromAPI?.length) {
      const transformedLanguages = audioLanguagesFromAPI.map((lang) => ({
        id: lang.languageId,
        name: lang.name,
        localName: lang.localName,
        codes: lang.codes,
        selected: lang.languageId === 'en',
      }));
      setData(transformedLanguages);
      setSelectedValue('English');
    }
  };

  const handleStreamingQualityData = () => {
    if (videoResolutionsFromAPI?.length > 0) {
      const transformedQuality = videoResolutionsFromAPI.map((resolution) => ({
        id: resolution.resolutionId,
        name: resolution.description,
        description: `Video quality: ${resolution.description}`,
        selected: resolution.resolutionId === 'auto', // Default to Auto
      }));
      setData(transformedQuality);
      setSelectedValue('Auto');
    }
  };

  const onItemSelect = (item) => {
    setSelectedValue(item.name);

    switch (preferenceType) {
      case PREFERENCE_TYPES.SUBTITLE:
        handleSubtitleSelect(item);
        break;
      case PREFERENCE_TYPES.AUDIO:
        handleAudioSelect(item);
        break;
      case PREFERENCE_TYPES.STREAMING_QUALITY:
        handleStreamingQualitySelect(item);
        break;
    }
  };

  const handleSubtitleSelect = (subtitle) => {
    // Here you would typically save the selection to state/storage
    console.log('Selected subtitle language:', subtitle);
  };

  const handleAudioSelect = (language) => {
     console.log('Selected audio language:', language);
    // Save audio preference to API
    if (profileId && accessToken) {
      setSaving(true);
      const audioPreferences = {
        languageId: language.id,
        // You can add bitrateId here if needed
        // bitrateId: "300"
      };

      dispatch(updateProfileAudioPreference(
        profileId,
        audioPreferences,
        accessToken,
        (response) => {
          console.log('Audio preference saved successfully:', response);
          setSaving(false);
        },
        (error) => {
          console.error('Failed to save audio preference:', error);
          setSaving(false);
        }
      ));
    }

    console.log('Selected audio language:', language);
  };

  const handleStreamingQualitySelect = (quality) => {
    console.log('Selected streaming quality:', quality);
  };

  const getTitle = () => {
    switch (preferenceType) {
      case PREFERENCE_TYPES.SUBTITLE:
        return getText('subtitle_language');
      case PREFERENCE_TYPES.AUDIO:
        return getText('audio_language');
      case PREFERENCE_TYPES.STREAMING_QUALITY:
        return getText('streaming_quality');
      default:
        return 'Profile Preference';
    }
  };

  const renderItemContent = (item) => {
    // For languages, show both name and localName if different
    if (preferenceType !== PREFERENCE_TYPES.STREAMING_QUALITY && item.localName && item.localName !== item.name) {
      return (
        <View>
          <Text style={{ fontSize: 16, color: '#FFFFFF' }}>{item.name}</Text>
          <Text style={{ fontSize: 14, color: '#CCCCCC' }}>{item.localName}</Text>
        </View>
      );
    }
    return null; // Use default rendering for streaming quality and simple language items
  };

  const getItemStyle = () => {
    // Custom style for streaming quality items
    if (preferenceType === PREFERENCE_TYPES.STREAMING_QUALITY) {
      return {
        minHeight: utils.isTab ? normalizeHeight(70) : normalizeHeight(60),
      };
    }
    return undefined;
  };

  return (
    <ProfilePreferenceList
      title={getTitle()}
      data={data}
      selectedValue={selectedValue}
      onItemSelect={onItemSelect}
      renderItemContent={renderItemContent}
      itemStyle={getItemStyle()}
      disabled={saving}
    />
  );
};

export default ProfilePreferenceDynamic;
