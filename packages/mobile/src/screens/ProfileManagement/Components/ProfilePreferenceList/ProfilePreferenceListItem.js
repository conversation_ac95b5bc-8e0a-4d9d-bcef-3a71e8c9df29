import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import styles from './styles';

const ProfilePreferenceListItem = ({
  item,
  selectedValue,
  onItemSelect,
  renderItemContent,
  itemStyle,
}) => {
  const defaultRenderItemContent = (item) => (
    <Text style={styles.itemName}>{item.name}</Text>
  );

  return (
    <TouchableOpacity
      style={[styles.listItem, itemStyle]}
      onPress={() => onItemSelect(item)}
    >
      <View style={styles.itemContent}>
        {renderItemContent
          ? renderItemContent(item)
          : defaultRenderItemContent(item)}
      </View>
      {selectedValue === item.name && (
        <View style={styles.checkmark}>
          <Text style={styles.checkmarkIcon}>✓</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

export default ProfilePreferenceListItem;
