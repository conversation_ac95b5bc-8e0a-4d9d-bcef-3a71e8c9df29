import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import useTranslate from 'mobile/src/hooks/useTranslate';
import ProfilePreferenceList from '../../Components/ProfilePreferenceList';
import ProfilePreferenceLanguageItem from '../../Components/ProfilePreferenceLanguageItem';

const ProfileSubtitleLanguage = () => {
  const { getText } = useTranslate();
  // Subscribe to only subtitleLanguages from Redux
  const subtitleLanguagesFromAPI = useSelector(
    (state) => state.auth.profilePreferences?.subtitleLanguages || []
  );
  const [selectedSubtitle, setSelectedSubtitle] = useState('English');
  const [subtitleLanguages, setSubtitleLanguages] = useState([]);

  useEffect(() => {
    // Transform API data to component format
    if (subtitleLanguagesFromAPI?.length) {
      const transformedLanguages = subtitleLanguagesFromAPI.map((lang) => ({
        id: lang.languageId,
        name: lang.name,
        localName: lang.localName,
        codes: lang.codes,
        selected: lang.languageId === 'en', // Default to English
      }));

      // Add "Off" option for subtitles
      transformedLanguages.push({
        id: 'off',
        name: 'Off',
        localName: 'Off',
        codes: [],
        selected: false,
      });

      setSubtitleLanguages(transformedLanguages);
    }
  }, [subtitleLanguagesFromAPI]);

  const onSubtitleSelect = (subtitle) => {
    setSelectedSubtitle(subtitle.name);
    // Here you would typically save the selection to state/storage
    console.log('Selected subtitle language:', subtitle);
  };

  const renderItemContent = (item) => (
    <ProfilePreferenceLanguageItem item={item} />
  );

  return (
    <ProfilePreferenceList
      title={getText('subtitle_language') || 'Subtitle Language'}
      data={subtitleLanguages}
      selectedValue={selectedSubtitle}
      onItemSelect={onSubtitleSelect}
      renderItemContent={renderItemContent}
    />
  );
};

export default ProfileSubtitleLanguage;
